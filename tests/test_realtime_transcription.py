#!/usr/bin/env python3
"""
Test script for VoiceHype realtime transcription WebSocket server
Tests both OpenAI and AssemblyAI services with live audio capture

Requirements:
pip install pyaudio websockets asyncio

Usage:
python test_realtime_transcription.py --api-key YOUR_API_KEY --service openai --model gpt-4o-mini-transcribe
python test_realtime_transcription.py --api-key YOUR_API_KEY --service assemblyai --model best
"""

import asyncio
import json
import pyaudio
import websockets
import argparse
import sys
import threading
import time
from typing import Optional

# Audio configuration
CHUNK = 1024  # Audio chunk size
FORMAT = pyaudio.paInt16  # 16-bit audio
CHANNELS = 1  # Mono audio
RATE = 16000  # 16kHz sample rate (required for realtime transcription)

class RealtimeTranscriptionTester:
    def __init__(self, api_key: str, service: str = 'openai', model: str = 'gpt-4o-mini-transcribe',
                 language: str = 'en', server_url: str = 'supabase.voicehype.ai'):
        self.api_key = api_key
        self.service = service
        self.model = model
        self.language = language
        self.server_url = server_url

        # Audio setup
        self.audio = pyaudio.PyAudio()
        self.stream: Optional[pyaudio.Stream] = None
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None

        # State tracking
        self.is_recording = False
        self.is_connected = False
        self.transcript_segments = []
        self.partial_transcripts = []
        self.complete_transcript = ""

        # Statistics
        self.start_time = None
        self.audio_bytes_sent = 0
        self.messages_received = 0

    def list_audio_devices(self):
        """List available audio input devices"""
        print("\n=== Available Audio Input Devices ===")
        for i in range(self.audio.get_device_count()):
            info = self.audio.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                print(f"Device {i}: {info['name']} (Channels: {info['maxInputChannels']}, Rate: {info['defaultSampleRate']})")
        print()

    async def connect_websocket(self):
        """Connect to the realtime transcription WebSocket"""
        # Construct WebSocket URL
        ws_url = f"wss://{self.server_url}/functions/v1/transcribe/realtime"
        params = {
            'apiKey': self.api_key,
            'service': self.service,
            'model': self.model,
            'language': self.language
        }

        # Add query parameters
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        full_url = f"{ws_url}?{query_string}"

        print(f"Connecting to: {ws_url}")
        print(f"Service: {self.service}, Model: {self.model}, Language: {self.language}")

        try:
            # Connect with custom headers
            extra_headers = {
                'User-Agent': 'VoiceHype-Python-Test-Client',
                'Origin': f'https://{self.server_url}'
            }

            self.websocket = await websockets.connect(
                full_url,
                extra_headers=extra_headers,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )

            print("✅ WebSocket connected successfully!")
            self.is_connected = True
            return True

        except Exception as e:
            print(f"❌ Failed to connect to WebSocket: {e}")
            return False

    def setup_audio_stream(self, device_index: Optional[int] = None):
        """Setup audio input stream"""
        try:
            self.stream = self.audio.open(
                format=FORMAT,
                channels=CHANNELS,
                rate=RATE,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=CHUNK
            )
            print(f"✅ Audio stream setup successful (Device: {device_index or 'default'}, Rate: {RATE}Hz)")
            return True
        except Exception as e:
            print(f"❌ Failed to setup audio stream: {e}")
            return False

    async def send_audio_data(self):
        """Continuously send audio data to WebSocket"""
        print("🎤 Starting audio capture and streaming...")
        self.start_time = time.time()

        try:
            while self.is_recording and self.websocket:
                # Read audio data
                audio_data = self.stream.read(CHUNK, exception_on_overflow=False)
                self.audio_bytes_sent += len(audio_data)

                # Send to WebSocket as binary data
                await self.websocket.send(audio_data)

                # Small delay to prevent overwhelming the server
                await asyncio.sleep(0.01)

        except Exception as e:
            print(f"❌ Error sending audio data: {e}")
        finally:
            print("🛑 Audio streaming stopped")

    async def receive_messages(self):
        """Receive and process messages from WebSocket"""
        try:
            async for message in self.websocket:
                self.messages_received += 1

                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError:
                    print(f"⚠️  Received non-JSON message: {message}")

        except websockets.exceptions.ConnectionClosed:
            print("🔌 WebSocket connection closed")
        except Exception as e:
            print(f"❌ Error receiving messages: {e}")

    async def handle_message(self, data: dict):
        """Handle different types of messages from the server"""
        msg_type = data.get('type', data.get('message_type', 'unknown'))

        if msg_type == 'connected':
            print(f"🔗 Session established: {data.get('sessionId', 'unknown')}")
            if 'maxDurationMs' in data:
                max_duration = data['maxDurationMs'] / 1000 / 60  # Convert to minutes
                print(f"⏱️  Maximum session duration: {max_duration:.1f} minutes")

        elif msg_type == 'partial' or msg_type == 'PartialTranscript':
            text = data.get('text', '')
            if text.strip():
                self.partial_transcripts.append(text)
                print(f"📝 Partial: {text}")

        elif msg_type == 'final' or msg_type == 'FinalTranscript':
            text = data.get('text', '')
            if text.strip():
                self.transcript_segments.append(text)
                print(f"✅ Final: {text}")

        elif msg_type == 'CompleteTranscript':
            text = data.get('text', '')
            if text.strip():
                self.complete_transcript = text
                print(f"🎯 Complete Transcript: {text}")

        elif msg_type == 'duration':
            duration = data.get('duration', 0)
            print(f"⏱️  Session duration: {duration:.2f} seconds")

        elif msg_type == 'finalized':
            print(f"🏁 Session finalized: {data.get('message', 'Complete')}")

        elif msg_type == 'error':
            print(f"❌ Error: {data.get('message', 'Unknown error')}")

        elif msg_type == 'timeout':
            print(f"⏰ Timeout: {data.get('message', 'Session timeout')}")

        elif msg_type == 'service_disconnected':
            print(f"🔌 Service disconnected: {data.get('reason', 'Unknown reason')}")

        elif msg_type == 'heartbeat':
            # Server heartbeat - just acknowledge it quietly
            heartbeat_num = data.get('debug', {}).get('heartbeatNumber', 'unknown')
            session_duration = data.get('debug', {}).get('sessionDuration', 0)
            if session_duration:
                duration_seconds = session_duration / 1000
                print(f"💓 Heartbeat #{heartbeat_num} (session: {duration_seconds:.1f}s)")

        elif msg_type == 'pong':
            # Response to our ping
            response_time = data.get('debug', {}).get('roundTripTime', -1)
            if response_time > 0:
                print(f"🏓 Pong received (RTT: {response_time}ms)")

        elif msg_type == 'status':
            print(f"ℹ️  Status: {data.get('message', 'Unknown status')}")

        elif msg_type == 'warning':
            print(f"⚠️  Warning: {data.get('message', 'Unknown warning')}")

        elif msg_type == 'reconnecting':
            attempt = data.get('attempt', 'unknown')
            max_attempts = data.get('maxAttempts', 'unknown')
            print(f"🔄 Reconnecting... (attempt {attempt}/{max_attempts})")

        else:
            print(f"📨 Unknown message type '{msg_type}': {data}")

    async def close_session(self):
        """Send close message and cleanup"""
        if self.websocket and not self.websocket.closed:
            print("📤 Sending close message to server...")
            try:
                await self.websocket.send(json.dumps({
                    'type': 'close',
                    'requestFinalTranscript': True
                }))

                # Wait a bit for final messages
                await asyncio.sleep(2)

            except Exception as e:
                print(f"⚠️  Error sending close message: {e}")

            try:
                await self.websocket.close()
            except Exception as e:
                print(f"⚠️  Error closing WebSocket: {e}")

    def cleanup(self):
        """Cleanup audio resources"""
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
        self.audio.terminate()

    def print_statistics(self):
        """Print session statistics"""
        if self.start_time:
            duration = time.time() - self.start_time
            print(f"\n=== Session Statistics ===")
            print(f"Duration: {duration:.2f} seconds")
            print(f"Audio bytes sent: {self.audio_bytes_sent:,} bytes")
            print(f"Messages received: {self.messages_received}")
            print(f"Final transcript segments: {len(self.transcript_segments)}")
            print(f"Partial transcripts: {len(self.partial_transcripts)}")

            if self.complete_transcript:
                print(f"\n=== Complete Transcript ===")
                print(f'"{self.complete_transcript}"')
            elif self.transcript_segments:
                print(f"\n=== Assembled Transcript ===")
                assembled = ' '.join(self.transcript_segments)
                print(f'"{assembled}"')
            else:
                print("\n⚠️  No transcript received")

    async def run_test(self, duration_seconds: int = 30, device_index: Optional[int] = None):
        """Run the complete test"""
        print("🚀 Starting VoiceHype Realtime Transcription Test")
        print("=" * 50)

        # Setup audio
        if not self.setup_audio_stream(device_index):
            return False

        # Connect WebSocket
        if not await self.connect_websocket():
            self.cleanup()
            return False

        try:
            # Start receiving messages
            receive_task = asyncio.create_task(self.receive_messages())

            # Wait for connection to be established
            await asyncio.sleep(2)

            # Start recording and streaming
            self.is_recording = True
            audio_task = asyncio.create_task(self.send_audio_data())

            print(f"🎙️  Recording for {duration_seconds} seconds... Speak now!")
            print("Press Ctrl+C to stop early")

            # Wait for specified duration or until interrupted
            try:
                await asyncio.sleep(duration_seconds)
            except KeyboardInterrupt:
                print("\n⏹️  Recording interrupted by user")

            # Stop recording
            self.is_recording = False

            # Wait for audio task to complete
            try:
                await asyncio.wait_for(audio_task, timeout=2)
            except asyncio.TimeoutError:
                audio_task.cancel()

            # Close session and wait for final messages
            await self.close_session()

            # Cancel receive task
            receive_task.cancel()
            try:
                await receive_task
            except asyncio.CancelledError:
                pass

            return True

        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
        finally:
            self.cleanup()
            self.print_statistics()

async def main():
    parser = argparse.ArgumentParser(description='Test VoiceHype realtime transcription')
    parser.add_argument('--api-key', required=True, help='VoiceHype API key')
    parser.add_argument('--service', choices=['openai', 'assemblyai'], default='openai', help='Transcription service')
    parser.add_argument('--model', help='Model to use (default: gpt-4o-mini-transcribe for OpenAI, best for AssemblyAI)')
    parser.add_argument('--language', default='en', help='Language code (default: en)')
    parser.add_argument('--duration', type=int, default=30, help='Recording duration in seconds (default: 30)')
    parser.add_argument('--device', type=int, help='Audio input device index (use --list-devices to see options)')
    parser.add_argument('--list-devices', action='store_true', help='List available audio devices and exit')
    parser.add_argument('--server', default='supabase.voicehype.ai', help='Server URL (default: supabase.voicehype.ai)')

    args = parser.parse_args()

    # Set default models based on service
    if not args.model:
        args.model = 'gpt-4o-mini-transcribe' if args.service == 'openai' else 'best'

    # Create tester instance
    tester = RealtimeTranscriptionTester(
        api_key=args.api_key,
        service=args.service,
        model=args.model,
        language=args.language,
        server_url=args.server
    )

    # List devices if requested
    if args.list_devices:
        tester.list_audio_devices()
        tester.cleanup()
        return

    # Run the test
    success = await tester.run_test(args.duration, args.device)

    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
