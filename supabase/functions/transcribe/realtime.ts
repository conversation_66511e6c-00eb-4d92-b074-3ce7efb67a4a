// @ts-ignore: Deno types
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
// @ts-ignore: Supabase types
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';
import {
    createErrorResponse,
    createSuccessResponse,
    ErrorCode
} from '../_shared/utils.ts';

// Declare Deno types
declare const Deno: {
    env: {
        get(key: string): string | undefined;
    };
    upgradeWebSocket(req: Request): { socket: WebSocket; response: Response };
};

// Create a Supabase client for database operations
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Get API key from environment
const ASSEMBLYAI_API_KEY = Deno.env.get('ASSEMBLYAI_API_KEY') || '';

if (!ASSEMBLYAI_API_KEY) {
    throw new Error('Missing required AssemblyAI API key');
}

// Constants
const MAX_SESSION_DURATION_MINUTES = 20; // Maximum session duration in minutes

// Supported services and models
const SUPPORTED_SERVICES = ['assemblyai'] as const;
type TranscriptionService = typeof SUPPORTED_SERVICES[number];

const SUPPORTED_MODELS = {
    assemblyai: ['best'] as const
} as const;

type AssemblyAIModel = typeof SUPPORTED_MODELS['assemblyai'][number];
type SupportedModel = AssemblyAIModel;

// Helper function to get headers as an object
function getHeadersAsObject(headers: Headers): Record<string, string> {
    const obj: Record<string, string> = {};
    headers.forEach((value, key) => {
        obj[key] = value;
    });
    return obj;
}

// Function to generate a temporary token for AssemblyAI real-time transcription
async function generateAssemblyAITemporaryToken(expiresIn: number = 480): Promise<string> {
    console.log('Generating temporary token for AssemblyAI');

    try {
        const response = await fetch('https://api.assemblyai.com/v2/realtime/token', {
            method: 'POST',
            headers: {
                'Authorization': ASSEMBLYAI_API_KEY,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                expires_in: expiresIn
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error generating AssemblyAI token:', response.status, errorText);
            throw new Error(`Failed to generate AssemblyAI token: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        console.log('Successfully generated AssemblyAI temporary token');
        return data.token;
    } catch (error) {
        console.error('Error generating AssemblyAI token:', error);
        throw error;
    }
}

// No OpenAI/LemonFox real-time transcription function needed as we only support AssemblyAI

// Function to connect to AssemblyAI's real-time transcription service
async function connectToAssemblyAI(
    socket: WebSocket,
    userId: string,
    apiKeyId: string,
    model: AssemblyAIModel,
    sessionId: string,
    availableMinutes: number
): Promise<void> {
    let isConnected = false;
    let assemblyAISocket: WebSocket | null = null;
    let sessionStartTime = Date.now();
    let audioBytesSent = 0;
    let transcriptionReceived = false;
    let sessionTimeout: number | null = null;
    let heartbeatInterval: number | null = null;
    let reconnectAttempts = 0;
    const MAX_RECONNECT_ATTEMPTS = 3;
    
    // For tracking the final transcript
    let finalTranscript = "";
    let lastPartialTranscript = "";

    // For tracking connection health
    let lastClientMessageTime = Date.now();
    let lastAssemblyAIMessageTime = Date.now();
    let lastHeartbeatSentTime = 0;
    let heartbeatResponsesReceived = 0;
    let heartbeatsSent = 0;

    // For tracking successfully processed audio chunks
    let successfulAudioChunks = 0;
    let totalAudioSamples = 0;
    const sampleRate = 16000; // Default sample rate for real-time audio
    const channels = 1; // Default channels for real-time audio
    const bitsPerSample = 16; // Default bits per sample for real-time audio

    // Set a timeout for the maximum session duration (minimum of available minutes or 20 minutes)
    const maxDurationMs = Math.min(availableMinutes, MAX_SESSION_DURATION_MINUTES) * 60 * 1000;

    console.log(`[DEBUG] Starting AssemblyAI connection setup for session ${sessionId} at ${new Date().toISOString()}`, {
        model: model,
        userId: userId,
        apiKeyId: apiKeyId,
        availableMinutes: availableMinutes,
        maxDurationMs: maxDurationMs,
        modelForPricing: `assemblyai/${model}-realtime`,
        sessionStartTime: new Date(sessionStartTime).toISOString()
    });

    try {
        // Get pricing model information first
        const { data: pricingCheck } = await supabase
            .rpc('check_usage_allowance', {
                p_user_id: userId,
                p_service: 'transcription',
                p_model: `assemblyai/${model}-realtime`,
                p_amount: 1, // Check for 1 minute to determine pricing model
                p_api_key_id: apiKeyId,
                p_is_input_only: false
            });

        const pricingModel = pricingCheck && pricingCheck.length > 0
            ? pricingCheck[0].pricing_model
            : 'credits';

        console.log(`Initial pricing model for session ${sessionId}: ${pricingModel}`);

        // Record pending usage with the correct pricing model
        await supabase
            .from('usage_history')
            .insert({
                user_id: userId,
                api_key_id: apiKeyId,
                service: 'transcription',
                model: `assemblyai/${model}-realtime`,
                amount: 0, // Will be updated when the session ends
                cost: 0,   // Will be updated when the session ends
                pricing_model: pricingModel, // Use the determined pricing model
                status: 'pending',
                metadata: {
                    sessionId,
                    startTime: sessionStartTime,
                    maxDurationMs
                }
            });

        console.log(`Recorded pending usage for session ${sessionId} with pricing model ${pricingModel}`);

        // Set up session timeout
        sessionTimeout = setTimeout(() => {
            console.log(`Session ${sessionId} reached maximum duration of ${maxDurationMs}ms`);

            // Send timeout notification to client
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'timeout',
                    message: 'Session reached maximum duration'
                }));
            }

            // Clear heartbeat interval if it exists
            if (heartbeatInterval !== null) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
            }

            // Close AssemblyAI connection if still open
            if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                assemblyAISocket.close();
            }

            // Close client connection if still open
            if (socket.readyState === WebSocket.OPEN) {
                socket.close();
            }
        }, maxDurationMs);

        // Function to establish connection to AssemblyAI
        const connectToAssemblyAIService = async () => {
            // Generate a temporary token for AssemblyAI
            // Use a longer expiration time to prevent token expiration issues
            const tokenExpirationSeconds = Math.min(availableMinutes * 60 + 300, 1800); // Add 5 minutes buffer, max 30 minutes
            const temporaryToken = await generateAssemblyAITemporaryToken(tokenExpirationSeconds);

            console.log(`Got AssemblyAI token for session ${sessionId}:`, {
                tokenLength: temporaryToken.length,
                tokenPrefix: temporaryToken.substring(0, 5) + '...',
                expirationSeconds: tokenExpirationSeconds
            });

            // Connect to AssemblyAI's real-time transcription service
            // Use the token directly in the URL as a query parameter to avoid subprotocol issues
            const assemblyAIUrl = `wss://api.assemblyai.com/v2/realtime/ws?sample_rate=16000&token=${temporaryToken}`;

            console.log(`Creating AssemblyAI WebSocket connection for session ${sessionId}`);

            // Create WebSocket connection without using subprotocols
            return new WebSocket(assemblyAIUrl);
        };

        // Establish initial connection
        assemblyAISocket = await connectToAssemblyAIService();

        assemblyAISocket.onopen = () => {
            console.log(`Connected to AssemblyAI for session ${sessionId}`);
            isConnected = true;
            reconnectAttempts = 0; // Reset reconnect attempts on successful connection

            // Set up a heartbeat to keep the connection alive
            // Send a ping every 30 seconds to prevent the connection from timing out
            if (heartbeatInterval !== null) {
                clearInterval(heartbeatInterval);
            }

            heartbeatInterval = setInterval(() => {
                const now = Date.now();
                const timeSinceLastClientMessage = now - lastClientMessageTime;
                const timeSinceLastAssemblyAIMessage = now - lastAssemblyAIMessageTime;

                console.log(`[DEBUG] Heartbeat check for session ${sessionId} at ${new Date().toISOString()}:`, {
                    timeSinceLastClientMessage: `${Math.round(timeSinceLastClientMessage/1000)}s`,
                    timeSinceLastAssemblyAIMessage: `${Math.round(timeSinceLastAssemblyAIMessage/1000)}s`,
                    heartbeatsSent,
                    heartbeatResponsesReceived,
                    responseRate: heartbeatsSent > 0 ? (heartbeatResponsesReceived / heartbeatsSent) : 0,
                    clientSocketState: socket.readyState,
                    assemblyAISocketState: assemblyAISocket?.readyState || 'null',
                    isConnected,
                    audioBytesSent,
                    transcriptionReceived,
                    sessionDuration: `${Math.round((now - sessionStartTime)/1000)}s`
                });

                if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                    lastHeartbeatSentTime = now;
                    heartbeatsSent++;

                    try {
                        // Send a ping message with timestamp to track round-trip time
                        assemblyAISocket.send(JSON.stringify({
                            type: "ping",
                            timestamp: now,
                            debug: {
                                heartbeatNumber: heartbeatsSent,
                                timeSinceLastClientMessage,
                                timeSinceLastAssemblyAIMessage,
                                sessionDuration: now - sessionStartTime
                            }
                        }));

                        console.log(`[DEBUG] Sent heartbeat #${heartbeatsSent} to AssemblyAI for session ${sessionId}`);

                        // Also send a heartbeat to the client
                        if (socket.readyState === WebSocket.OPEN) {
                            socket.send(JSON.stringify({
                                type: 'heartbeat',
                                timestamp: now,
                                debug: {
                                    heartbeatNumber: heartbeatsSent,
                                    audioBytesSent,
                                    transcriptionReceived,
                                    sessionDuration: now - sessionStartTime
                                }
                            }));
                            console.log(`[DEBUG] Sent heartbeat status to client for session ${sessionId}`);
                        } else {
                            console.log(`[DEBUG] Client socket not open (state: ${socket.readyState}), couldn't send heartbeat`);
                        }
                    } catch (error) {
                        console.error(`[DEBUG] Error sending heartbeat for session ${sessionId}:`, error);
                    }

                    // Check if we haven't received a message from AssemblyAI in too long
                    if (timeSinceLastAssemblyAIMessage > 60000) { // 1 minute
                        console.error(`[DEBUG] No message from AssemblyAI in ${Math.round(timeSinceLastAssemblyAIMessage/1000)}s for session ${sessionId}, connection may be stalled`);

                        // Notify client of potential stall
                        if (socket.readyState === WebSocket.OPEN) {
                            socket.send(JSON.stringify({
                                type: 'warning',
                                message: `No response from transcription service in ${Math.round(timeSinceLastAssemblyAIMessage/1000)}s, connection may be stalled`,
                                timestamp: now
                            }));
                        }
                    }
                } else {
                    // Clear the interval if the socket is closed
                    console.log(`[DEBUG] AssemblyAI socket not open (state: ${assemblyAISocket?.readyState || 'null'}), clearing heartbeat interval`);
                    if (heartbeatInterval !== null) {
                        clearInterval(heartbeatInterval);
                        heartbeatInterval = null;
                    }
                }
            }, 15000); // 15 seconds interval (reduced from 30s for more frequent checks)

            // Notify client that connection is ready
            socket.send(JSON.stringify({
                type: 'connected',
                sessionId: sessionId,
                maxDurationMs: maxDurationMs
            }));
        };

        assemblyAISocket.onmessage = (event) => {
            // Update last message time from AssemblyAI
            lastAssemblyAIMessageTime = Date.now();                // Forward transcription results to the client
            try {
                const data = JSON.parse(event.data);
                const messageType = data.message_type || 'unknown';

                console.log(`[DEBUG] Received AssemblyAI message for session ${sessionId} at ${new Date().toISOString()}:`, {
                    type: messageType,
                    hasText: !!data.text,
                    textLength: data.text?.length || 0,
                    timeSinceSessionStart: `${Math.round((Date.now() - sessionStartTime)/1000)}s`,
                    audioBytesSent
                });

                // Only forward final transcripts, store partial ones for later use
                if (messageType === 'FinalTranscript') {
                    transcriptionReceived = true;
                    if (data.text && data.text.trim().length > 0) {
                        // Add to the final transcript
                        finalTranscript += data.text + " ";
                        console.log(`[DEBUG] Added final transcript segment for session ${sessionId}, total length: ${finalTranscript.length}, appended: "${data.text}"`);
                    }
                    console.log(`[DEBUG] Forwarding ${messageType} to client for session ${sessionId}, text length: ${data.text?.length || 0}`);
                    socket.send(event.data);
                } else if (messageType === 'PartialTranscript') {
                    // Only mark that we've received some transcription, but don't forward partial results
                    transcriptionReceived = true;
                    if (data.text && data.text.trim().length > 0) {
                        // Update the last partial transcript (for backup)
                        lastPartialTranscript = data.text;
                        console.log(`[DEBUG] Updated lastPartialTranscript for session ${sessionId}, length: ${lastPartialTranscript.length}`);
                    }
                    console.log(`[DEBUG] Received ${messageType} but not forwarding to client for session ${sessionId}, text length: ${data.text?.length || 0}`);
                } else if (messageType === 'SessionBegins') {
                    console.log(`[DEBUG] AssemblyAI session started for ${sessionId} at ${new Date().toISOString()}:`, data);

                    // Send a status update to the client
                    socket.send(JSON.stringify({
                        type: 'status',
                        message: 'Session started successfully',
                        sessionId: sessionId,
                        timestamp: Date.now()
                    }));
                } else if (messageType === 'Error') {
                    console.error(`[DEBUG] AssemblyAI error for session ${sessionId} at ${new Date().toISOString()}:`, data);
                    socket.send(JSON.stringify({
                        type: 'error',
                        message: data.text || 'Error from transcription service',
                        timestamp: Date.now(),
                        details: data
                    }));
                } else if (messageType === 'pong' || messageType === 'ping') {
                    // Heartbeat response
                    heartbeatResponsesReceived++;
                    const responseTime = lastHeartbeatSentTime > 0 ? Date.now() - lastHeartbeatSentTime : -1;

                    console.log(`[DEBUG] Received heartbeat response #${heartbeatResponsesReceived} for session ${sessionId}, response time: ${responseTime}ms, success rate: ${heartbeatResponsesReceived}/${heartbeatsSent}`);
                } else {
                    // Unknown message type
                    console.log(`[DEBUG] Received unknown message type from AssemblyAI: ${messageType} for session ${sessionId}`);
                }
            } catch (error) {
                console.error(`[DEBUG] Error parsing AssemblyAI message for session ${sessionId} at ${new Date().toISOString()}:`, {
                    error: error instanceof Error ? error.message : String(error),
                    rawData: typeof event.data === 'string' ? event.data.substring(0, 200) + (event.data.length > 200 ? '...' : '') : 'Binary data',
                    dataType: typeof event.data,
                    dataLength: typeof event.data === 'string' ? event.data.length : 'unknown'
                });
            }
        };

        assemblyAISocket.onerror = (event) => {
            console.error(`AssemblyAI WebSocket error for session ${sessionId}:`, event);

            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'error',
                    message: 'Error in transcription service',
                    details: 'WebSocket connection error'
                }));
            }

            // Attempt to reconnect if we haven't exceeded the maximum number of attempts
            if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                reconnectAttempts++;
                console.log(`Attempting to reconnect (attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}) for session ${sessionId}`);

                // Clear existing heartbeat interval
                if (heartbeatInterval !== null) {
                    clearInterval(heartbeatInterval);
                    heartbeatInterval = null;
                }

                // Try to reconnect after a short delay
                setTimeout(async () => {
                    try {
                        if (assemblyAISocket) {
                            assemblyAISocket.close();
                        }
                        assemblyAISocket = await connectToAssemblyAIService();
                    } catch (reconnectError) {
                        console.error(`Failed to reconnect for session ${sessionId}:`, reconnectError);
                    }
                }, 2000); // 2 second delay before reconnecting
            }
        };

        assemblyAISocket.onclose = (event) => {
            console.log(`AssemblyAI WebSocket closed for session ${sessionId}`, {
                code: event.code,
                reason: event.reason,
                wasClean: event.wasClean,
                transcriptionReceived: transcriptionReceived,
                audioBytesSent: audioBytesSent,
                durationMs: Date.now() - sessionStartTime
            });

            // Handle specific close codes
            if (event.code === 4000) {
                console.error(`AssemblyAI error: Invalid parameters`);
            } else if (event.code === 4001) {
                console.error(`AssemblyAI error: Authentication failed`);
            } else if (event.code === 4101) {
                console.error(`AssemblyAI error: Invalid request format - ${event.reason}`);
            }

            // Clear heartbeat interval
            if (heartbeatInterval !== null) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
            }

            // Attempt to reconnect if the connection was not closed cleanly and we haven't exceeded the maximum attempts
            if (!event.wasClean && reconnectAttempts < MAX_RECONNECT_ATTEMPTS && socket.readyState === WebSocket.OPEN) {
                reconnectAttempts++;
                console.log(`Connection closed unexpectedly. Attempting to reconnect (attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}) for session ${sessionId}`);

                // Notify client about reconnection attempt
                socket.send(JSON.stringify({
                    type: 'reconnecting',
                    attempt: reconnectAttempts,
                    maxAttempts: MAX_RECONNECT_ATTEMPTS
                }));

                // Try to reconnect after a short delay
                setTimeout(async () => {
                    try {
                        assemblyAISocket = await connectToAssemblyAIService();
                    } catch (reconnectError) {
                        console.error(`Failed to reconnect for session ${sessionId}:`, reconnectError);

                        // If this was our last attempt, close the client connection
                        if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS && socket.readyState === WebSocket.OPEN) {
                            socket.send(JSON.stringify({
                                type: 'service_disconnected',
                                code: event.code,
                                reason: 'Failed to reconnect after multiple attempts',
                                transcriptionReceived: transcriptionReceived
                            }));
                            socket.close();

                            // Finalize the session
                            finalizeSessionAndCleanup();
                        }
                    }
                }, 2000); // 2 second delay before reconnecting

                return; // Don't proceed with finalization if we're attempting to reconnect
            }

            isConnected = false;

            // Clear the session timeout if we're not reconnecting
            if (sessionTimeout !== null) {
                clearTimeout(sessionTimeout);
                sessionTimeout = null;
            }

            // Notify client that AssemblyAI connection is closed if we're not reconnecting
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'service_disconnected',
                    code: event.code,
                    reason: event.reason,
                    transcriptionReceived: transcriptionReceived,
                    details: event.reason || 'Connection closed'
                }));

                // Close client connection if not already closed
                socket.close();
            }

            // Finalize the session and clean up
            finalizeSessionAndCleanup();
        };

        // Helper function to finalize the session and clean up resources
        const finalizeSessionAndCleanup = () => {
            // Calculate actual audio duration based on samples processed
            let calculatedAudioDuration = 0;
            if (totalAudioSamples > 0) {
                // Duration = totalSamples / sampleRate
                calculatedAudioDuration = totalAudioSamples / sampleRate;
                console.log(`Calculated audio duration for session ${sessionId}:`, {
                    totalAudioSamples,
                    sampleRate,
                    calculatedAudioDuration,
                    successfulAudioChunks
                });
            } else {
                // Fallback to session duration if no samples were processed
                calculatedAudioDuration = (Date.now() - sessionStartTime) / 1000;
                console.log(`Using session duration for session ${sessionId} (no audio samples processed):`, {
                    sessionDuration: calculatedAudioDuration,
                    audioBytesSent
                });
            }

            // Record final session usage
            finalizeSession(
                userId,
                apiKeyId,
                `assemblyai/${model}-realtime`,
                sessionId,
                sessionStartTime,
                audioBytesSent,
                transcriptionReceived,
                undefined,
                calculatedAudioDuration
            );
            
            // Prepare the complete transcript for the client
            const completeTranscript = finalTranscript.trim().length > 0 ? finalTranscript.trim() : lastPartialTranscript.trim();
            console.log(`Final transcript for session ${sessionId}: ${completeTranscript.length > 0 ? 
                `"${completeTranscript.substring(0, 100)}${completeTranscript.length > 100 ? '...' : ''}" (${completeTranscript.length} chars)` : 
                'No transcript available'}`);
            
            // If socket is still open, make sure to send the complete transcript
            if (socket.readyState === WebSocket.OPEN) {
                console.log(`[DEBUG] Socket still open during finalizeSessionAndCleanup, ensuring complete transcript is sent for session ${sessionId}`);
            }
        };

        socket.onmessage = (event) => {
            try {
                // Update last message time from client
                lastClientMessageTime = Date.now();

                // Check if the message is binary audio data
                if (event.data instanceof ArrayBuffer) {
                    if (isConnected && assemblyAISocket?.readyState === WebSocket.OPEN) {
                        audioBytesSent += event.data.byteLength;

                        // Calculate number of audio samples in this chunk
                        // Each sample is bitsPerSample/8 bytes * channels
                        const bytesPerSample = (bitsPerSample / 8) * channels;
                        const samplesInChunk = Math.floor(event.data.byteLength / bytesPerSample);
                        totalAudioSamples += samplesInChunk;

                        // Increment successful chunks counter
                        successfulAudioChunks++;

                        // Log audio chunk details (but not too frequently)
                        if (successfulAudioChunks % 20 === 0) { // Log every 20th chunk
                            console.log(`[DEBUG] Forwarding audio chunk #${successfulAudioChunks} for session ${sessionId} at ${new Date().toISOString()}:`, {
                                byteLength: event.data.byteLength,
                                samplesInChunk,
                                totalAudioSamples,
                                estimatedDurationSoFar: `${(totalAudioSamples / sampleRate).toFixed(2)}s`,
                                timeSinceSessionStart: `${((Date.now() - sessionStartTime) / 1000).toFixed(2)}s`
                            });
                        }

                        assemblyAISocket.send(event.data);
                    } else if (!isConnected) {
                        console.log(`[DEBUG] Cannot forward audio for session ${sessionId}: AssemblyAI not connected (state: ${assemblyAISocket?.readyState || 'null'})`);
                    }
                } else {
                    // Handle JSON control messages from client
                    const message = JSON.parse(event.data);
                    console.log(`[DEBUG] Received client control message for session ${sessionId} at ${new Date().toISOString()}:`, message);

                    if (message.type === 'close') {
                        // Client requested to close the connection
                        console.log(`[DEBUG] Client requested close for session ${sessionId} at ${new Date().toISOString()}, requestFinalTranscript=${message.requestFinalTranscript || false}`);

                        if (message.requestFinalTranscript) {
                            // Prepare and send the complete transcript immediately
                            const completeTranscript = finalTranscript.trim().length > 0 ? 
                                finalTranscript.trim() : lastPartialTranscript.trim();
                            
                            if (completeTranscript.length > 0) {
                                console.log(`[DEBUG] Sending complete transcript for session ${sessionId}, length: ${completeTranscript.length}`);
                                
                                // Send the complete transcript
                                socket.send(JSON.stringify({
                                    message_type: 'CompleteTranscript',
                                    text: completeTranscript,
                                    sessionId: sessionId
                                }));
                                
                                // Wait a bit before finalizing to ensure the message gets delivered
                                setTimeout(() => {
                                    console.log(`[DEBUG] Finalizing session after sending complete transcript for ${sessionId}`);
                                    // Send the finalization message
                                    socket.send(JSON.stringify({
                                        type: 'finalized',
                                        message: 'Session finalized successfully',
                                        sessionId: sessionId
                                    }));
                                    
                                    // Now close connections after the delay
                                    if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                                        assemblyAISocket.close();
                                    }
                                    
                                    if (sessionTimeout !== null) {
                                        clearTimeout(sessionTimeout);
                                        sessionTimeout = null;
                                    }
                                    
                                    socket.close();
                                }, 1000); // 1 second delay to ensure messages are delivered
                                
                                return; // Skip the immediate close below
                            } else {
                                console.log(`[DEBUG] No transcript available to send for session ${sessionId}`);
                            }
                        }
                        
                        // If we don't have a transcript or requestFinalTranscript is false, close immediately
                        if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                            assemblyAISocket.close();
                        }

                        if (sessionTimeout !== null) {
                            clearTimeout(sessionTimeout);
                            sessionTimeout = null;
                        }

                        socket.close();
                    } else if (message.type === 'ping') {
                        // Client sent a ping, respond with a pong
                        console.log(`[DEBUG] Received ping from client for session ${sessionId}, responding with pong`);
                        if (socket.readyState === WebSocket.OPEN) {
                            socket.send(JSON.stringify({
                                type: 'pong',
                                timestamp: Date.now(),
                                debug: {
                                    receivedTimestamp: message.timestamp,
                                    roundTripTime: message.timestamp ? Date.now() - message.timestamp : -1,
                                    audioBytesSent,
                                    transcriptionReceived,
                                    sessionDuration: Date.now() - sessionStartTime
                                }
                            }));
                        }
                    }
                }
            } catch (error) {
                console.error(`[DEBUG] Error handling client message for session ${sessionId} at ${new Date().toISOString()}:`, {
                    error: error instanceof Error ? error.message : String(error),
                    stack: error instanceof Error ? error.stack : 'No stack trace',
                    dataType: typeof event.data,
                    isBinary: event.data instanceof ArrayBuffer,
                    dataSize: event.data instanceof ArrayBuffer ? event.data.byteLength :
                             (typeof event.data === 'string' ? event.data.length : 'unknown')
                });
            }
        };

        socket.onerror = (event) => {
            console.error(`Client WebSocket error for session ${sessionId}:`, event);
        };

        socket.onclose = () => {
            console.log(`Client WebSocket closed for session ${sessionId}`);

            // Clear the session timeout
            if (sessionTimeout !== null) {
                clearTimeout(sessionTimeout);
                sessionTimeout = null;
            }

            // Close AssemblyAI connection if still open
            if (assemblyAISocket && assemblyAISocket.readyState === WebSocket.OPEN) {
                assemblyAISocket.close();
            }

            // Calculate actual audio duration based on samples processed
            let calculatedAudioDuration = 0;
            if (totalAudioSamples > 0) {
                // Duration = totalSamples / sampleRate
                calculatedAudioDuration = totalAudioSamples / sampleRate;
                console.log(`Calculated audio duration for session ${sessionId}:`, {
                    totalAudioSamples,
                    sampleRate,
                    calculatedAudioDuration,
                    successfulAudioChunks
                });
            } else {
                // Fallback to session duration if no samples were processed
                calculatedAudioDuration = (Date.now() - sessionStartTime) / 1000;
                console.log(`Using session duration for session ${sessionId} (no audio samples processed):`, {
                    sessionDuration: calculatedAudioDuration,
                    audioBytesSent
                });
            }

            // Record final session usage
            finalizeSession(
                userId,
                apiKeyId,
                `assemblyai/${model}-realtime`,
                sessionId,
                sessionStartTime,
                audioBytesSent,
                transcriptionReceived,
                undefined,
                calculatedAudioDuration
            );
        };
    } catch (error) {
        console.error(`Error setting up AssemblyAI connection for session ${sessionId}:`, error);

        if (socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify({
                type: 'error',
                message: 'Failed to establish connection with AssemblyAI',
                details: error.message
            }));
            socket.close();
        }

        // Clear the session timeout
        if (sessionTimeout !== null) {
            clearTimeout(sessionTimeout);
            sessionTimeout = null;
        }
    }

    // After finalizing the session, send the duration information to the client
    if (socket.readyState === WebSocket.OPEN) {
        // Calculate duration in seconds
        const durationSeconds = (Date.now() - sessionStartTime) / 1000;

        // Send duration information to client
        socket.send(JSON.stringify({
            type: 'duration',
            duration: durationSeconds,
            processingComplete: true,
            sessionId: sessionId
        }));

        // Send the full transcript to the client
        const completeTranscript = finalTranscript.trim().length > 0 ? finalTranscript.trim() : lastPartialTranscript.trim();
        
        if (completeTranscript.length > 0) {
            console.log(`[DEBUG] Sending complete transcript for session ${sessionId}, length: ${completeTranscript.length}, sample: "${completeTranscript.substring(0, 50)}${completeTranscript.length > 50 ? '...' : ''}"`);
            
            // Send the complete transcript
            socket.send(JSON.stringify({
                message_type: 'CompleteTranscript',
                text: completeTranscript,
                sessionId: sessionId
            }));
            
            // Add a small delay to ensure the message is sent before potentially closing
            setTimeout(() => {
                // Then send the finalization confirmation
                console.log(`[DEBUG] Sending finalization message for session ${sessionId}`);
                socket.send(JSON.stringify({
                    type: 'finalized',
                    message: 'Session finalized successfully',
                    sessionId: sessionId
                }));
            }, 500); // 500ms delay to ensure the transcript is processed first
        } else {
            console.log(`[DEBUG] No complete transcript available for session ${sessionId}`);
            
            // Send finalization message even if no transcript is available
            socket.send(JSON.stringify({
                type: 'finalized',
                message: 'Session finalized successfully but no transcript was generated',
                sessionId: sessionId
            }));
        }
    } else {
        console.log(`[DEBUG] Cannot send complete transcript for session ${sessionId}: Socket not open (state: ${socket.readyState})`);
    }
}

// Helper function to finalize a session and record usage
async function finalizeSession(
    userId: string,
    apiKeyId: string,
    model: string,
    sessionId: string,
    startTime: number,
    audioBytesSent: number,
    transcriptionReceived: boolean,
    tokenInfo?: { input_tokens: number, output_tokens: number },
    processedDurationSeconds?: number
): Promise<void> {
    console.log(`Finalizing session ${sessionId} for model ${model}`, {
        audioBytesSent,
        transcriptionReceived,
        durationMs: Date.now() - startTime,
        hasTokenInfo: !!tokenInfo,
        processedDurationSeconds
    });

    try {
        // Calculate duration in minutes for billing
        const durationMinutes = processedDurationSeconds
            ? processedDurationSeconds / 60
            : (Date.now() - startTime) / (1000 * 60);

        // Round to 2 decimal places for billing accuracy
        const roundedDurationMinutes = Math.round(durationMinutes * 100) / 100;

        // Get pricing model information
        const { data: pricingCheck } = await supabase
            .rpc('check_usage_allowance', {
                p_user_id: userId,
                p_service: 'transcription',
                p_model: model,
                p_amount: roundedDurationMinutes,
                p_api_key_id: apiKeyId,
                p_is_input_only: false
            });

        const pricingModel = pricingCheck && pricingCheck.length > 0
            ? pricingCheck[0].pricing_model
            : 'credits';

        console.log(`Pricing model for session ${sessionId}: ${pricingModel}`);

        // First, update the pricing_model in the usage_history record
        // This ensures the record has the correct pricing_model before finalization
        const { error: updateError } = await supabase
            .from('usage_history')
            .update({ pricing_model: pricingModel })
            .match({
                user_id: userId,
                service: 'transcription',
                model: model,
                status: 'pending'
            })
            .order('created_at', { ascending: false })
            .limit(1);

        if (updateError) {
            console.error(`Error updating pricing model for session ${sessionId}:`, updateError);
        } else {
            console.log(`Successfully updated pricing model to ${pricingModel} for session ${sessionId}`);
        }

        // Get cost per minute
        const { data: servicePricing } = await supabase
            .from('service_pricing')
            .select('cost_per_unit')
            .eq('service', 'transcription')
            .eq('model', model)
            .eq('is_active', true)
            .single();

        const costPerUnit = servicePricing?.cost_per_unit || 0;
        const totalCost = costPerUnit * roundedDurationMinutes;

        // Use finalize_usage instead of finalize_realtime_session
        await supabase.rpc('finalize_usage', {
            p_user_id: userId,
            p_service: 'transcription',
            p_model: model,
            p_amount: roundedDurationMinutes,
            p_cost: totalCost,
            p_pricing_model: pricingModel,
            p_metadata: {
                sessionId,
                startTime,
                endTime: Date.now(),
                audioBytesSent,
                hasTranscription: transcriptionReceived,
                processedDurationSeconds,
                calculatedDurationMinutes: roundedDurationMinutes
            }
        });

        console.log(`Successfully finalized session ${sessionId} with duration ${roundedDurationMinutes} minutes, cost ${totalCost}, and pricing model ${pricingModel}`);
    } catch (error) {
        console.error(`Error finalizing session ${sessionId}:`, error);
    }
}

// Export the handler function for use in index.ts
export async function handleRealTimeRequest(req: Request): Promise<Response> {
    console.log('Received realtime transcription request:', {
        url: req.url,
        method: req.method,
        upgrade: req.headers.get('upgrade'),
        origin: req.headers.get('origin'),
        host: req.headers.get('host'),
    });

    try {
        // Check if the request is a WebSocket upgrade request
        const upgrade = req.headers.get('upgrade') || '';
        if (upgrade.toLowerCase() !== 'websocket') {
            console.error('Request is not trying to upgrade to WebSocket');
            return new Response("Request isn't trying to upgrade to WebSocket.", { status: 400 });
        }

        // Get API key and service parameters from URL
        const url = new URL(req.url);
        const apiKey = url.searchParams.get('apiKey');
        const service = (url.searchParams.get('service') || 'assemblyai') as TranscriptionService;
        const model = url.searchParams.get('model') || '';
        const language = url.searchParams.get('language') || 'en';

        console.log('Request parameters:', {
            service,
            model,
            language,
            hasApiKey: !!apiKey,
            apiKeyLength: apiKey ? apiKey.length : 0
        });

        if (!apiKey) {
            console.error('Missing API key in query parameters');
            return new Response('Missing API key', { status: 401 });
        }

        // Validate service - only AssemblyAI is supported for real-time transcription
        if (service !== 'assemblyai') {
            return createErrorResponse(400, 'Only AssemblyAI is supported for real-time transcription', ErrorCode.INVALID_REQUEST);
        }

        // If no model is specified, use 'best' as the default
        const selectedModel = model || 'best';

        // Validate the model - only 'best' is supported for AssemblyAI real-time
        if (selectedModel !== 'best') {
            return createErrorResponse(400, 'Only the "best" model is supported for AssemblyAI real-time transcription', ErrorCode.UNSUPPORTED_MODEL);
        }

        console.log('Validating API key:', {
            keyLength: apiKey.length,
            keyPrefix: apiKey.substring(0, 5) + '...'
        });

        // Validate VoiceHype API key and get user info
        const { data: validationData, error: validationError } = await supabase
            .rpc('validate_api_key', { p_key: apiKey });

        if (validationError || !validationData || validationData.length === 0) {
            console.error('API key validation failed:', validationError);
            return createErrorResponse(401, 'Invalid API key', ErrorCode.UNAUTHORIZED);
        }

        const userId = validationData[0].user_id;
        const apiKeyId = validationData[0].api_key_id;

        console.log('API key validated successfully for user:', userId);
        console.log(`Real-time transcription request: service=${service}, model=${selectedModel}, language=${language}`);

        // Check for unpaid balances
        const { data: hasUnpaidBalance, error: balanceCheckError } = await supabase
            .rpc('has_unpaid_payg_balance', { p_user_id: userId });

        if (balanceCheckError) {
            console.error('Error checking for unpaid balances:', balanceCheckError);
            // Continue anyway with the operation
        } else if (hasUnpaidBalance) {
            // Get detailed unpaid balance information
            const { data: unpaidBalances } = await supabase
                .rpc('get_unpaid_payg_balances', { p_user_id: userId });

            console.log('Unpaid balances:', {
                hasUnpaidBalance,
                balances: unpaidBalances
            });

            return createErrorResponse(
                402,
                'You have unpaid PAYG balances from previous months. Please settle your outstanding balance before using the service.',
                ErrorCode.UNPAID_BALANCE
            );
        }

        // Prepare model name for database queries
        const modelForDb = `assemblyai/${selectedModel}-realtime`;

        // Get available minutes from user credits
        const { data: pricingCheck, error: usagePricingError } = await supabase
            .rpc('check_usage_allowance', {
                p_user_id: userId,
                p_service: 'transcription',
                p_model: modelForDb,
                p_amount: 1, // Check for 1 minute to get the cost per minute
                p_api_key_id: apiKeyId,
                p_is_input_only: false // Explicitly set this to resolve function overloading
            });

        console.log('Usage allowance check result:', {
            modelChecked: modelForDb,
            result: pricingCheck?.[0] || null,
            error: usagePricingError || null
        });

        if (usagePricingError) {
            console.error('Error checking usage allowance:', usagePricingError);
            return createErrorResponse(500, 'Error checking usage allowance', ErrorCode.SERVICE_ERROR);
        }

        if (!pricingCheck || pricingCheck.length === 0 || !pricingCheck[0].can_use) {
            console.error('Insufficient usage allowance:', {
                modelRequested: modelForDb,
                pricingCheck: pricingCheck?.[0] || null,
                service: 'transcription'
            });

            const errorCode = pricingCheck && pricingCheck[0]?.error_code
                ? pricingCheck[0].error_code
                : ErrorCode.INSUFFICIENT_CREDITS;

            return createErrorResponse(
                402,
                'Insufficient credits or quota for this operation',
                errorCode as ErrorCode
            );
        }

        // Get service pricing for this model
        const { data: servicePricing, error: servicePricingError } = await supabase
            .from('service_pricing')
            .select('*')
            .eq('service', 'transcription')
            .eq('model', modelForDb)
            .eq('is_active', true)
            .single();

        console.log('Service pricing check:', {
            modelRequested: modelForDb,
            found: !!servicePricing,
            pricing: servicePricing ? {
                cost_per_unit: servicePricing.cost_per_unit,
                unit: servicePricing.unit
            } : null,
            error: servicePricingError ? servicePricingError.message : null
        });

        if (servicePricingError || !servicePricing) {
            console.error('Error fetching service pricing:', {
                error: servicePricingError,
                service: 'transcription',
                model: modelForDb
            });
            return createErrorResponse(500, 'Error fetching service pricing', ErrorCode.SERVICE_ERROR);
        }

        // Get user's credits
        const { data: credits, error: creditsError } = await supabase
            .from('credits')
            .select('balance')
            .eq('user_id', userId)
            .single();

        if (creditsError) {
            console.error('Error fetching user credits:', creditsError);
            return createErrorResponse(500, 'Error fetching user credits', ErrorCode.SERVICE_ERROR);
        }

        // Calculate available minutes based on credits and cost per minute
        const costPerMinute = servicePricing.cost_per_unit;
        const availableMinutes = credits ? Math.floor(credits.balance / costPerMinute) : 0;

        console.log('Available transcription minutes:', {
            userId,
            creditsBalance: credits?.balance || 0,
            costPerMinute,
            availableMinutes,
            maxSessionMinutes: Math.min(availableMinutes, MAX_SESSION_DURATION_MINUTES),
            modelRequested: modelForDb
        });

        // Check if user has enough credits for at least one minute
        if (availableMinutes < 1) {
            console.error('Insufficient credits for real-time transcription:', {
                creditsBalance: credits?.balance || 0,
                costPerMinute,
                availableMinutes,
                modelRequested: modelForDb
            });
            return createErrorResponse(402, 'Insufficient credits for real-time transcription (minimum 1 minute required)',
                ErrorCode.INSUFFICIENT_CREDITS);
        }

        console.log('Ready to upgrade WebSocket connection, for service:', service, 'model:', selectedModel);

        // Upgrade the connection to WebSocket
        try {
            const { socket, response } = Deno.upgradeWebSocket(req);

            // Create a session ID for tracking
            const sessionId = crypto.randomUUID();
            console.log(`Successfully upgraded WebSocket connection for session ${sessionId}`);

            // Connect to AssemblyAI
            await connectToAssemblyAI(
                socket,
                userId,
                apiKeyId,
                selectedModel as AssemblyAIModel,
                sessionId,
                availableMinutes
            );

            // Return the WebSocket response
            return response;
        } catch (upgradeError: any) {
            console.error('Error upgrading WebSocket connection:', upgradeError.message, upgradeError.stack);
            return new Response(`Failed to upgrade WebSocket connection: ${upgradeError.message}`, {
                status: 500
            });
        }
    } catch (error: any) {
        console.error('Error in real-time transcription:', error);
        return createErrorResponse(
            500,
            `Real-time transcription error: ${error.message}`,
            ErrorCode.SERVICE_ERROR
        );
    }
}

// Keep the serve function for local testing
// Deno-specific way to check if this is the main module
// @ts-ignore: Deno-specific feature
if (typeof Deno !== "undefined" && Deno.mainModule === import.meta.url) {
    console.log("Starting real-time transcription server...");
    serve(handleRealTimeRequest);
}